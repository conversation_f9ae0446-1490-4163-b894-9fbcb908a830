const duplicateScanner = require('./src/duplicateScanner');

// Test the duplicate detection logic
async function testDuplicateLogic() {
    console.log('Testing duplicate detection logic...');
    
    // Test extractBaseName function
    const testFiles = [
        '2. Vv đôn đốc nộp phí sử dung kho số viễn thông Quý 2 năm 2021 (lần 1) của Công ty cổ phần Cát Tiên Sa Play.docx',
        '2. Vv đôn đốc nộp phí sử dung kho số viễn thông Quý 2 năm 2021 (lần 1) của <PERSON>ông ty cổ phần Cát Tiên Sa Play_1.docx',
        '2. Vv đôn đốc nộp phí sử dung kho số viễn thông Quý 2 năm 2021 (lần 1) của Công ty cổ phần Cát Tiên Sa Play_2.docx'
    ];
    
    console.log('\nTesting extractBaseName:');
    testFiles.forEach(fileName => {
        const baseName = duplicateScanner.extractBaseName(fileName);
        console.log(`File: ${fileName}`);
        console.log(`Base: ${baseName}`);
        console.log('---');
    });
    
    // Test shouldSkipPath function
    const testPaths = [
        'System Volume Information',
        '$RECYCLE.BIN',
        'normal-folder',
        'pagefile.sys',
        'document.txt'
    ];
    
    console.log('\nTesting shouldSkipPath:');
    testPaths.forEach(path => {
        const shouldSkip = duplicateScanner.shouldSkipPath(path);
        console.log(`Path: ${path} -> Skip: ${shouldSkip}`);
    });
}

testDuplicateLogic().catch(console.error);
