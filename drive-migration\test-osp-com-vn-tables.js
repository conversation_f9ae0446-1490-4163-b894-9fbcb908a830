#!/usr/bin/env node

/**
 * Test script để kiểm tra kết nối với các bảng osp.com.vn trên Supabase
 */

import dotenv from 'dotenv';
import { SupabaseClient } from './src/database/supabase.js';

dotenv.config();

async function testOspComVnTables() {
    console.log('🧪 Testing osp.com.vn tables connection...');
    console.log('='.repeat(60));
    
    try {
        const supabase = new SupabaseClient();
        const serviceClient = supabase.getServiceClient();
        
        // Test connection
        console.log('🔌 Testing database connection...');
        const { data: testData, error: testError } = await serviceClient
            .from(process.env.SCANNED_FILES_TABLE_NAME || 'scanned_files')
            .select('id')
            .limit(1);
            
        if (testError) {
            throw new Error(`Database connection failed: ${testError.message}`);
        }
        console.log('✅ Database connection successful');
        console.log('');
        
        // Test all tables
        console.log('📋 Testing all osp.com.vn tables...');
        const tables = [
            { name: process.env.USERS_TABLE_NAME || 'users', description: 'Users mapping table' },
            { name: process.env.SCANNED_SESSIONS_TABLE_NAME || 'scan_sessions', description: 'Scan sessions table' },
            { name: process.env.SCANNED_FILES_TABLE_NAME || 'scanned_files', description: 'Scanned files table' },
            { name: process.env.SCANNED_USERS_TABLE_NAME || 'scanned_users', description: 'Scanned users table' },
            { name: process.env.USER_STORAGE_STATS_TABLE_NAME || 'user_storage_stats', description: 'User storage stats table' },
            { name: process.env.DOWNLOAD_SESSIONS_TABLE_NAME || 'download_sessions', description: 'Download sessions table' },
            { name: process.env.DOWNLOAD_ITEMS_TABLE_NAME || 'download_items', description: 'Download items table' },
            { name: process.env.SCANNED_LARK_FILES_TABLE_NAME || 'scanned_lark_files', description: 'Scanned Lark files table' },
            { name: process.env.MIGRATION_TASKS_TABLE_NAME || 'migration_tasks', description: 'Migration tasks table' },
            { name: process.env.MIGRATION_ITEMS_TABLE_NAME || 'migration_items', description: 'Migration items table' },
            { name: process.env.PERMISSION_MAPPINGS_TABLE_NAME || 'permission_mappings', description: 'Permission mappings table' },
            { name: process.env.MIGRATION_LOGS_TABLE_NAME || 'migration_logs', description: 'Migration logs table' }
        ];
        
        const results = {};
        
        for (const table of tables) {
            try {
                const { data, error, count } = await serviceClient
                    .from(table.name)
                    .select('*', { count: 'exact', head: true });
                    
                if (error) {
                    console.log(`❌ ${table.name}: ${error.message}`);
                    results[table.name] = { status: 'error', error: error.message };
                } else {
                    console.log(`✅ ${table.name}: ${count || 0} records - ${table.description}`);
                    results[table.name] = { status: 'success', count: count || 0 };
                }
            } catch (err) {
                console.log(`❌ ${table.name}: ${err.message}`);
                results[table.name] = { status: 'error', error: err.message };
            }
        }
        
        console.log('');
        console.log('📊 Summary:');
        const successCount = Object.values(results).filter(r => r.status === 'success').length;
        const errorCount = Object.values(results).filter(r => r.status === 'error').length;
        
        console.log(`✅ Successful tables: ${successCount}`);
        console.log(`❌ Failed tables: ${errorCount}`);
        
        if (errorCount === 0) {
            console.log('');
            console.log('🎉 All osp.com.vn tables are accessible!');
            
            // Test specific osp_com_vn_scanned_files table
            console.log('');
            console.log('🔍 Testing main scanned_files table...');
            const { data: files, error: filesError, count: filesCount } = await serviceClient
                .from(process.env.SCANNED_FILES_TABLE_NAME)
                .select('id, name, user_email, mime_type, size', { count: 'exact' })
                .limit(5);
                
            if (filesError) {
                console.log(`❌ Error querying scanned_files: ${filesError.message}`);
            } else {
                console.log(`✅ Found ${filesCount} files in scanned_files table`);
                if (files && files.length > 0) {
                    console.log('📄 Sample files:');
                    files.forEach((file, index) => {
                        console.log(`   ${index + 1}. ${file.name} (${file.user_email}) - ${file.mime_type}`);
                    });
                }
            }
        } else {
            console.log('');
            console.log('⚠️ Some tables are not accessible. Please check the errors above.');
        }
        
    } catch (error) {
        console.error('💥 Test failed:', error.message);
        process.exit(1);
    }
}

// Run the test
testOspComVnTables();
