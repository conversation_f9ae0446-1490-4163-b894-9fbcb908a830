{"name": "duplicate-file-remover", "version": "1.0.0", "description": "A web-based tool to find and remove duplicate files", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "keywords": ["duplicate", "files", "remover", "web", "tool"], "author": "", "license": "MIT", "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "fs-extra": "^11.1.1", "node-fetch": "^3.3.2", "ws": "^8.14.2"}, "devDependencies": {"nodemon": "^3.0.1"}}