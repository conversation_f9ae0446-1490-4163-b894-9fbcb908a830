#!/usr/bin/env node

import { SupabaseClient } from '../database/supabase.js';
import dotenv from 'dotenv';

dotenv.config();

/**
 * Script để verify không còn file_id nào bị trùng trong bảng scanned_files
 */
async function verifyNoDuplicates() {
    console.log('🔍 Verifying no duplicate file_ids in scanned_files table...');
    console.log('='.repeat(60));
    
    try {
        const supabase = new SupabaseClient();
        const serviceClient = supabase.getServiceClient();

        // Test connection
        console.log('🔌 Testing database connection...');
        const { data: testData, error: testError } = await serviceClient
            .from('scanned_files')
            .select('id')
            .limit(1);

        if (testError) {
            throw new Error(`Database connection failed: ${testError.message}`);
        }
        console.log('✅ Database connection successful');
        console.log('');

        // Đếm tổng số bản ghi
        console.log('📊 Getting total record count...');
        const { count: totalCount, error: countError } = await serviceClient
            .from('scanned_files')
            .select('*', { count: 'exact', head: true });

        if (countError) throw countError;
        console.log(`📋 Total records in scanned_files: ${totalCount}`);
        console.log('');

        // Lấy tất cả file_id và đếm
        console.log('🔍 Analyzing all file_ids for duplicates...');
        const { data: allFiles, error: allError } = await serviceClient
            .from('scanned_files')
            .select('file_id, id, created_at, name')
            .order('file_id');

        if (allError) throw allError;

        console.log(`📋 Retrieved ${allFiles.length} records for analysis`);

        // Phân tích duplicates
        const fileIdGroups = {};
        allFiles.forEach(file => {
            if (!fileIdGroups[file.file_id]) {
                fileIdGroups[file.file_id] = [];
            }
            fileIdGroups[file.file_id].push(file);
        });

        // Tìm các file_id có nhiều hơn 1 bản ghi
        const duplicateGroups = Object.entries(fileIdGroups)
            .filter(([fileId, records]) => records.length > 1);

        console.log('');
        console.log('📊 ANALYSIS RESULTS:');
        console.log('-'.repeat(40));
        console.log(`🔢 Total unique file_ids: ${Object.keys(fileIdGroups).length}`);
        console.log(`🔄 File_ids with duplicates: ${duplicateGroups.length}`);

        if (duplicateGroups.length === 0) {
            console.log('');
            console.log('✅ SUCCESS: No duplicate file_ids found!');
            console.log('🎉 Your scanned_files table is clean!');
        } else {
            console.log('');
            console.log('⚠️ WARNING: Found duplicate file_ids:');
            console.log('');
            
            duplicateGroups.forEach(([fileId, records], index) => {
                console.log(`${index + 1}. file_id: ${fileId}`);
                console.log(`   📊 Count: ${records.length} records`);
                console.log(`   📋 Records:`);
                records.forEach((record, i) => {
                    console.log(`      ${i + 1}. ID: ${record.id}, Created: ${record.created_at}, Name: ${record.name}`);
                });
                console.log('');
            });

            // Tính tổng số bản ghi cần xóa
            const totalDuplicates = duplicateGroups.reduce((sum, [fileId, records]) => {
                return sum + (records.length - 1); // Trừ 1 vì giữ lại 1 bản ghi
            }, 0);

            console.log(`🗑️ Total duplicate records that should be removed: ${totalDuplicates}`);
        }

        console.log('');
        console.log('='.repeat(60));
        
        return {
            totalRecords: totalCount,
            uniqueFileIds: Object.keys(fileIdGroups).length,
            duplicateFileIds: duplicateGroups.length,
            duplicateGroups: duplicateGroups
        };

    } catch (error) {
        console.error('💥 Verification failed:', error.message);
        process.exit(1);
    }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    verifyNoDuplicates();
}

export { verifyNoDuplicates };
