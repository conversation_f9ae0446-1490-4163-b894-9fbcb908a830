<!DOCTYPE html>
<html>
<head>
    <title>Test Duplicate Logic</title>
</head>
<body>
    <h1>Test Duplicate Detection Logic</h1>
    <div id="results"></div>
    
    <script>
        // Test the extractBaseName logic in JavaScript
        function extractBaseName(fileName) {
            // Extract file name and extension
            const lastDotIndex = fileName.lastIndexOf('.');
            let nameWithoutExt, extension;
            
            if (lastDotIndex > 0) {
                nameWithoutExt = fileName.substring(0, lastDotIndex);
                extension = fileName.substring(lastDotIndex);
            } else {
                nameWithoutExt = fileName;
                extension = '';
            }
            
            // Check if this file has a _number suffix
            const numberedMatch = nameWithoutExt.match(/^(.+)_(\d+)$/);
            if (numberedMatch) {
                // This is a numbered file (e.g., "document_1")
                return numberedMatch[1] + extension;
            }
            
            // This might be the original file, return as potential base name
            return nameWithoutExt + extension;
        }
        
        // Test files
        const testFiles = [
            '2. Vv đôn đốc nộp phí sử dung kho số viễn thông Quý 2 năm 2021 (lần 1) của Công ty cổ phần Cát Tiên Sa Play.docx',
            '2. Vv đôn đốc nộp phí sử dung kho số viễn thông Quý 2 năm 2021 (lần 1) của Công ty cổ phần Cát Tiên Sa Play_1.docx',
            '2. Vv đôn đốc nộp phí sử dung kho số viễn thông Quý 2 năm 2021 (lần 1) của Công ty cổ phần Cát Tiên Sa Play_2.docx'
        ];
        
        let html = '<h2>Test Results:</h2>';
        
        testFiles.forEach(fileName => {
            const baseName = extractBaseName(fileName);
            html += `<p><strong>File:</strong> ${fileName}<br>`;
            html += `<strong>Base:</strong> ${baseName}</p><hr>`;
        });
        
        // Group by base name
        const groups = new Map();
        testFiles.forEach(fileName => {
            const baseName = extractBaseName(fileName);
            if (!groups.has(baseName)) {
                groups.set(baseName, []);
            }
            groups.get(baseName).push(fileName);
        });
        
        html += '<h2>Grouped Results:</h2>';
        for (const [baseName, files] of groups) {
            if (files.length > 1) {
                html += `<h3>Group: ${baseName}</h3><ul>`;
                files.forEach(file => {
                    html += `<li>${file}</li>`;
                });
                html += '</ul>';
            }
        }
        
        document.getElementById('results').innerHTML = html;
    </script>
</body>
</html>
