const fs = require('fs-extra');
const path = require('path');

class FileManager {
    async deleteFiles(filePaths, progressCallback) {
        const results = {
            successful: [],
            failed: [],
            total: filePaths.length
        };

        for (let i = 0; i < filePaths.length; i++) {
            const filePath = filePaths[i];
            const fileName = path.basename(filePath);

            try {
                if (progressCallback) {
                    progressCallback({
                        phase: 'deleting',
                        message: `Đang xóa: ${fileName}`,
                        processed: i,
                        total: filePaths.length,
                        currentFile: filePath,
                        fileName: fileName,
                        status: 'deleting'
                    });
                }

                // Check if file exists before attempting to delete
                const exists = await fs.pathExists(filePath);
                if (!exists) {
                    results.failed.push({
                        path: filePath,
                        error: 'File does not exist'
                    });

                    if (progressCallback) {
                        progressCallback({
                            phase: 'deleting',
                            message: `File không tồn tại: ${fileName}`,
                            processed: i + 1,
                            total: filePaths.length,
                            currentFile: filePath,
                            fileName: fileName,
                            status: 'error',
                            error: 'File does not exist'
                        });
                    }
                    continue;
                }

                // Attempt to delete the file
                await fs.remove(filePath);
                results.successful.push(filePath);

                if (progressCallback) {
                    progressCallback({
                        phase: 'deleting',
                        message: `Đã xóa: ${fileName}`,
                        processed: i + 1,
                        total: filePaths.length,
                        currentFile: filePath,
                        fileName: fileName,
                        status: 'success'
                    });
                }

            } catch (error) {
                results.failed.push({
                    path: filePath,
                    error: error.message
                });

                if (progressCallback) {
                    progressCallback({
                        phase: 'deleting',
                        message: `Lỗi xóa: ${fileName} - ${error.message}`,
                        processed: i + 1,
                        total: filePaths.length,
                        currentFile: filePath,
                        fileName: fileName,
                        status: 'error',
                        error: error.message
                    });
                }
            }
        }

        return results;
    }

    async getFileInfo(filePath) {
        try {
            const stats = await fs.stat(filePath);
            return {
                path: filePath,
                name: path.basename(filePath),
                size: stats.size,
                modified: stats.mtime,
                exists: true
            };
        } catch (error) {
            return {
                path: filePath,
                name: path.basename(filePath),
                exists: false,
                error: error.message
            };
        }
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

module.exports = new FileManager();
