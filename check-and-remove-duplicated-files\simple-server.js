const express = require('express');
const path = require('path');

const app = express();

// Middleware
app.use(express.json());
app.use(express.static('public'));

// Simple test route
app.get('/test', (req, res) => {
    res.json({ message: 'Server is working!' });
});

// Test duplicate scanner
app.post('/test-scanner', async (req, res) => {
    try {
        const duplicateScanner = require('./src/duplicateScanner');
        
        const testFiles = [
            '2. Vv đôn đốc nộp phí sử dung kho số viễn thông Quý 2 năm 2021 (lần 1) của <PERSON>ng ty cổ phần Cát Tiên Sa Play.docx',
            '2. Vv đôn đốc nộp phí sử dung kho số viễn thông Quý 2 năm 2021 (lần 1) của <PERSON>ông ty cổ phần Cát Tiên Sa Play_1.docx',
            '2. Vv đôn đốc nộp phí sử dung kho số viễn thông Quý 2 năm 2021 (lần 1) của <PERSON>ông ty cổ phần Cát Tiên Sa Play_2.docx'
        ];
        
        const results = testFiles.map(fileName => ({
            original: fileName,
            baseName: duplicateScanner.extractBaseName(fileName)
        }));
        
        res.json({ results });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

const PORT = 3001;
app.listen(PORT, () => {
    console.log(`Simple server running on port ${PORT}`);
    console.log(`Test URL: http://localhost:${PORT}/test`);
});
