#!/usr/bin/env node

/**
 * Test script để kiểm tra logic download với c<PERSON>c bảng osp.com.vn
 */

import dotenv from 'dotenv';
import { FileDownloadService } from './src/services/file-download-service.js';

dotenv.config();

async function testDownloadLogic() {
    console.log('🧪 Testing download logic with osp.com.vn tables...');
    console.log('='.repeat(60));
    
    try {
        const downloadService = new FileDownloadService();
        
        // Test 1: List available users
        console.log('👥 Testing available users...');
        const { SupabaseClient } = await import('./src/database/supabase.js');
        const supabase = new SupabaseClient();
        
        const { data: users, error: usersError } = await supabase.getServiceClient()
            .from(process.env.SCANNED_USERS_TABLE_NAME)
            .select('primary_email, full_name')
            .limit(5);
            
        if (usersError) {
            throw new Error(`Failed to get users: ${usersError.message}`);
        }
        
        console.log(`✅ Found ${users.length} sample users:`);
        users.forEach((user, index) => {
            console.log(`   ${index + 1}. ${user.primary_email} (${user.full_name})`);
        });
        
        if (users.length === 0) {
            console.log('⚠️ No users found. Cannot test download logic.');
            return;
        }
        
        // Test 2: Check files for first user
        const testUser = users[0];
        console.log('');
        console.log(`📁 Testing files for user: ${testUser.primary_email}...`);
        
        const { data: userFiles, error: filesError, count: filesCount } = await supabase.getServiceClient()
            .from(process.env.SCANNED_FILES_TABLE_NAME)
            .select('id, name, mime_type, size, download_status', { count: 'exact' })
            .eq('user_email', testUser.primary_email)
            .limit(10);
            
        if (filesError) {
            throw new Error(`Failed to get user files: ${filesError.message}`);
        }
        
        console.log(`✅ Found ${filesCount} files for ${testUser.primary_email}`);
        if (userFiles && userFiles.length > 0) {
            console.log('📄 Sample files:');
            userFiles.forEach((file, index) => {
                const sizeStr = file.size ? `${Math.round(file.size / 1024)}KB` : '0KB';
                const status = file.download_status || 'not_downloaded';
                console.log(`   ${index + 1}. ${file.name} (${file.mime_type}) - ${sizeStr} - ${status}`);
            });
        }
        
        // Test 3: Create a test download session (without actually downloading)
        console.log('');
        console.log('📦 Testing download session creation...');
        
        try {
            const session = await downloadService.createDownloadSession({
                name: `Test Session - ${new Date().toISOString()}`,
                selectedUsers: [testUser.primary_email],
                downloadPath: './test-downloads',
                concurrentDownloads: 1,
                maxRetries: 1,
                skipMimeTypes: ['application/vnd.google-apps.folder'], // Skip folders for test
                processingOrder: 'size_asc',
                stopOnError: false,
                continueOnError: true
            });
            
            console.log(`✅ Created test download session: ${session.id}`);
            console.log(`   - Name: ${session.name}`);
            console.log(`   - Total files: ${session.total_files}`);
            console.log(`   - Total size: ${downloadService.formatFileSize(session.total_size)}`);
            console.log(`   - Status: ${session.status}`);
            
            // Test 4: List download sessions
            console.log('');
            console.log('📋 Testing download sessions list...');
            
            const sessions = await downloadService.listSessions(5);
            console.log(`✅ Found ${sessions.length} download sessions:`);
            sessions.forEach((s, index) => {
                console.log(`   ${index + 1}. ${s.name} - ${s.status} (${s.total_files || 0} files)`);
            });
            
            // Test 5: Check download items were created
            console.log('');
            console.log('📝 Testing download items creation...');
            
            const { data: items, error: itemsError, count: itemsCount } = await supabase.getServiceClient()
                .from(process.env.DOWNLOAD_ITEMS_TABLE_NAME)
                .select('id, file_name, status', { count: 'exact' })
                .eq('download_session_id', session.id)
                .limit(5);
                
            if (itemsError) {
                throw new Error(`Failed to get download items: ${itemsError.message}`);
            }
            
            console.log(`✅ Created ${itemsCount} download items for session`);
            if (items && items.length > 0) {
                console.log('📄 Sample download items:');
                items.forEach((item, index) => {
                    console.log(`   ${index + 1}. ${item.file_name} - ${item.status}`);
                });
            }
            
            // Cleanup: Delete test session
            console.log('');
            console.log('🧹 Cleaning up test session...');
            
            const { error: deleteError } = await supabase.getServiceClient()
                .from(process.env.DOWNLOAD_SESSIONS_TABLE_NAME)
                .delete()
                .eq('id', session.id);
                
            if (deleteError) {
                console.log(`⚠️ Warning: Failed to cleanup test session: ${deleteError.message}`);
            } else {
                console.log('✅ Test session cleaned up successfully');
            }
            
        } catch (sessionError) {
            console.error(`❌ Download session test failed: ${sessionError.message}`);
        }
        
        console.log('');
        console.log('🎉 Download logic test completed successfully!');
        console.log('');
        console.log('📋 Summary:');
        console.log('✅ Database connection working');
        console.log('✅ Users table accessible');
        console.log('✅ Scanned files table accessible');
        console.log('✅ Download session creation working');
        console.log('✅ Download items creation working');
        console.log('✅ Environment variables properly configured');
        
    } catch (error) {
        console.error('💥 Test failed:', error.message);
        console.error(error.stack);
        process.exit(1);
    }
}

// Run the test
testDownloadLogic();
