console.log('Starting test server...');

try {
    const express = require('express');
    console.log('Express loaded');
    
    const app = express();
    console.log('Express app created');
    
    app.get('/', (req, res) => {
        res.send('Test server is working!');
    });
    
    const PORT = 3000;
    app.listen(PORT, () => {
        console.log(`Test server running on port ${PORT}`);
    });
    
} catch (error) {
    console.error('Error:', error);
    process.exit(1);
}
